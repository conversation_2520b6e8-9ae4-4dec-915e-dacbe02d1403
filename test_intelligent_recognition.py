#!/usr/bin/env python3
"""
Test script for Intelligent Face Recognition System

This script validates the intelligent recognition optimization system
and demonstrates the performance improvements.
"""

import sys
import os
import time
import numpy as np
from typing import List, Dict, Any

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_intelligent_recognition_import():
    """Test if intelligent recognition system can be imported."""
    print("=== Testing Intelligent Recognition Import ===")
    
    try:
        from intelligent_face_recognition import (
            IntelligentFaceRecognizer,
            HailoRecognitionBackend,
            SimpleCache,
            TrackState,
            FaceIdentity,
            TrackingInfo
        )
        print("✓ All intelligent recognition classes imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_cache_strategy():
    """Test the caching strategy logic."""
    print("\n=== Testing Cache Strategy ===")
    
    try:
        from intelligent_face_recognition import SimpleCache, TrackingInfo, TrackState, FaceIdentity
        
        cache = SimpleCache(rerecognition_interval=5, cache_timeout=10.0)
        
        # Test new face - should recognize
        new_track = TrackingInfo(tracker_id=1, state=TrackState.NEW)
        should_recognize = cache.should_recognize(new_track)
        print(f"New face should recognize: {should_recognize} ✓" if should_recognize else f"New face should recognize: {should_recognize} ✗")
        
        # Test tracked face with identity - should not recognize initially
        identity = FaceIdentity("test_person", 0.9, 0.3, last_seen=time.time())
        tracked_track = TrackingInfo(tracker_id=2, state=TrackState.TRACKED, identity=identity, frames_since_recognition=0)
        should_recognize = cache.should_recognize(tracked_track)
        print(f"Fresh tracked face should not recognize: {not should_recognize} ✓" if not should_recognize else f"Fresh tracked face should not recognize: {not should_recognize} ✗")
        
        # Test tracked face after interval - should recognize
        tracked_track.frames_since_recognition = 10  # > interval of 5
        should_recognize = cache.should_recognize(tracked_track)
        print(f"Tracked face after interval should recognize: {should_recognize} ✓" if should_recognize else f"Tracked face after interval should recognize: {should_recognize} ✗")
        
        # Test reappeared face - should recognize
        reappeared_track = TrackingInfo(tracker_id=3, state=TrackState.REAPPEARED)
        should_recognize = cache.should_recognize(reappeared_track)
        print(f"Reappeared face should recognize: {should_recognize} ✓" if should_recognize else f"Reappeared face should recognize: {should_recognize} ✗")
        
        return True
        
    except Exception as e:
        print(f"✗ Cache strategy test failed: {e}")
        return False

def test_performance_simulation():
    """Simulate performance improvements with intelligent recognition."""
    print("\n=== Performance Simulation ===")
    
    try:
        # Simulate processing 100 frames with 2 faces each
        total_frames = 100
        faces_per_frame = 2
        
        # Traditional approach: recognize every face in every frame
        traditional_recognitions = total_frames * faces_per_frame
        
        # Intelligent approach simulation:
        # - Frame 1: 2 new faces (2 recognitions)
        # - Frames 2-30: 2 tracked faces (0 recognitions due to caching)
        # - Frame 31: 2 faces re-recognized due to interval (2 recognitions)
        # - Frames 32-60: 2 tracked faces (0 recognitions)
        # - Frame 61: 2 faces re-recognized (2 recognitions)
        # - Frames 62-90: 2 tracked faces (0 recognitions)
        # - Frame 91: 2 faces re-recognized (2 recognitions)
        # - Frames 92-100: 2 tracked faces (0 recognitions)
        
        rerecognition_interval = 30
        intelligent_recognitions = 0
        
        for frame in range(1, total_frames + 1):
            if frame == 1 or frame % rerecognition_interval == 1:
                intelligent_recognitions += faces_per_frame
        
        savings = traditional_recognitions - intelligent_recognitions
        savings_percent = (savings / traditional_recognitions) * 100
        
        print(f"Traditional approach: {traditional_recognitions} recognitions")
        print(f"Intelligent approach: {intelligent_recognitions} recognitions")
        print(f"Savings: {savings} recognitions ({savings_percent:.1f}% reduction)")
        
        if savings_percent > 80:  # Expect significant savings
            print("✓ Performance optimization is significant")
            return True
        else:
            print("✗ Performance optimization is insufficient")
            return False
            
    except Exception as e:
        print(f"✗ Performance simulation failed: {e}")
        return False

def test_tracking_state_transitions():
    """Test tracking state transition logic."""
    print("\n=== Testing Tracking State Transitions ===")
    
    try:
        from intelligent_face_recognition import TrackState, TrackingInfo
        
        # Test state transitions
        states = [TrackState.NEW, TrackState.TRACKED, TrackState.LOST, TrackState.REAPPEARED]
        
        for state in states:
            track = TrackingInfo(tracker_id=1, state=state)
            print(f"Created track with state: {state.value} ✓")
        
        # Test state changes
        track = TrackingInfo(tracker_id=1, state=TrackState.NEW)
        track.state = TrackState.TRACKED
        print(f"State transition NEW -> TRACKED: {track.state.value} ✓")
        
        track.state = TrackState.LOST
        print(f"State transition TRACKED -> LOST: {track.state.value} ✓")
        
        track.state = TrackState.REAPPEARED
        print(f"State transition LOST -> REAPPEARED: {track.state.value} ✓")
        
        return True
        
    except Exception as e:
        print(f"✗ Tracking state test failed: {e}")
        return False

def test_bbox_overlap():
    """Test bounding box overlap calculation."""
    print("\n=== Testing Bounding Box Overlap ===")
    
    try:
        from intelligent_face_recognition import IntelligentFaceRecognizer
        
        # Create a dummy recognizer to test the overlap method
        class DummyBackend:
            def detect_faces(self, frame): return []
            def generate_embedding(self, frame, bbox): return None
            def recognize_embedding(self, embedding): return None
        
        recognizer = IntelligentFaceRecognizer(DummyBackend())
        
        # Test identical boxes - should be 1.0
        bbox1 = [100, 100, 200, 200]
        bbox2 = [100, 100, 200, 200]
        overlap = recognizer._bbox_overlap(bbox1, bbox2)
        print(f"Identical boxes overlap: {overlap:.2f} (expected: 1.00) {'✓' if abs(overlap - 1.0) < 0.01 else '✗'}")
        
        # Test non-overlapping boxes - should be 0.0
        bbox1 = [100, 100, 200, 200]
        bbox2 = [300, 300, 400, 400]
        overlap = recognizer._bbox_overlap(bbox1, bbox2)
        print(f"Non-overlapping boxes overlap: {overlap:.2f} (expected: 0.00) {'✓' if overlap == 0.0 else '✗'}")
        
        # Test partially overlapping boxes
        bbox1 = [100, 100, 200, 200]
        bbox2 = [150, 150, 250, 250]
        overlap = recognizer._bbox_overlap(bbox1, bbox2)
        expected = 0.25  # 50x50 intersection / (100x100 + 100x100 - 50x50) union
        print(f"Partially overlapping boxes overlap: {overlap:.2f} (expected: ~{expected:.2f}) {'✓' if abs(overlap - expected) < 0.01 else '✗'}")
        
        return True
        
    except Exception as e:
        print(f"✗ Bounding box overlap test failed: {e}")
        return False

def test_integration_readiness():
    """Test if the system is ready for integration."""
    print("\n=== Testing Integration Readiness ===")
    
    try:
        # Check if main script can import intelligent recognition
        import jk_face_recong_test
        
        has_intelligent_attr = hasattr(jk_face_recong_test, 'INTELLIGENT_RECOGNITION_AVAILABLE')
        print(f"Main script has intelligent recognition flag: {has_intelligent_attr} {'✓' if has_intelligent_attr else '✗'}")
        
        if has_intelligent_attr:
            available = jk_face_recong_test.INTELLIGENT_RECOGNITION_AVAILABLE
            print(f"Intelligent recognition available: {available} {'✓' if available else '✗'}")
        
        # Check if FaceRecognizer has intelligent methods
        recognizer_class = jk_face_recong_test.FaceRecognizer
        has_intelligent_method = hasattr(recognizer_class, 'detect_and_embed_intelligent')
        print(f"FaceRecognizer has intelligent method: {has_intelligent_method} {'✓' if has_intelligent_method else '✗'}")
        
        return has_intelligent_attr and has_intelligent_method
        
    except Exception as e:
        print(f"✗ Integration readiness test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing Intelligent Face Recognition System")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_intelligent_recognition_import),
        ("Cache Strategy", test_cache_strategy),
        ("Performance Simulation", test_performance_simulation),
        ("Tracking States", test_tracking_state_transitions),
        ("Bounding Box Overlap", test_bbox_overlap),
        ("Integration Readiness", test_integration_readiness)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    
    all_passed = True
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Intelligent recognition system is ready.")
        print("\nTo use intelligent recognition:")
        print("  python jk_face_recong_test.py --enable_intelligent_recognition --mode ui")
        print("  python jk_face_recong_test.py --enable_intelligent_recognition --enable_tracking --mode console")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
