#!/usr/bin/env python3
"""
embed_all_per_person.py

- Walks a root folder containing person subfolders (each contains aligned face images)
- Runs DeGirum detection+embedding on each image
- Stores EVERY embedding as a separate vector in Chroma with id "<person>_<imagebase>"
- Computes a per-person centroid (mean of L2-normalized embeddings) and upserts it as "<person>__centroid"
- Batches DB inserts for performance and handles duplicate-id upsert fallback.

Usage example:
  python embed_all_per_person.py --root_folder ~/faces --zoo_path ~/degirum-zoo --batch_size 32 --min_score 0.5 --overwrite_centroid
"""

import os
import time
import argparse
import logging
import numpy as np
import cv2
import degirum as dg
import chromadb

from typing import Optional, List, Tuple

logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")


REF_KPS_112 = np.array([
    [38.2946, 51.6963],
    [73.5318, 51.5014],
    [56.0252, 71.7366],
    [41.5493, 92.3655],
    [70.7299, 92.2041]
], dtype=np.float32)


# -----------------------
# Utility / DeGirum helpers
# -----------------------
def connect_zoo(zoo_path: Optional[str]):
    zoo_path = os.path.expanduser(zoo_path) if zoo_path else None
    logging.info("Connecting to DeGirum zoo: %s", zoo_path)
    return dg.connect(dg.LOCAL, zoo_path)


def safe_get_results(pred_out) -> List[dict]:
    """
    Accept either object with .results or raw list.
    Returns a list of result dicts (possibly empty).
    """
    if pred_out is None:
        return []
    if hasattr(pred_out, "results"):
        rs = getattr(pred_out, "results") or []
        return list(rs)
    if isinstance(pred_out, (list, tuple)):
        return list(pred_out)
    if isinstance(pred_out, dict):
        return [pred_out]
    return []


def safe_extract_embedding(result: dict) -> Optional[np.ndarray]:
    """
    Look for common keys that contain embedding arrays.
    Returns None if no numeric embedding found.
    """
    # Common candidate keys (variants)
    candidates = ['embedding', 'embeddings', 'vector', 'feat', 'features', 'features_vector']
    for k, v in result.items():
        if k.lower() in candidates:
            try:
                arr = np.asarray(v, dtype=float).flatten()
                if arr.size > 0:
                    return arr
            except Exception:
                pass

    # fallback: find first array-like numeric field
    for k, v in result.items():
        try:
            arr = np.asarray(v, dtype=float).flatten()
            if arr.size > 0:
                logging.debug("safe_extract_embedding: using fallback key '%s' for embedding", k)
                return arr
        except Exception:
            continue
    return None


def safe_parse_landmarks(raw_lm, w: int, h: int) -> Optional[np.ndarray]:
    if raw_lm is None:
        return None
    try:
        arr = np.asarray(raw_lm, dtype=float).flatten()
    except Exception:
        try:
            # try nested list flatten
            flat = []
            for item in raw_lm:
                flat.extend(np.asarray(item).flatten().tolist())
            arr = np.asarray(flat, dtype=float)
        except Exception:
            return None
    if arr.size == 10:
        arr = arr.reshape(5, 2)
    else:
        try:
            arr = arr.reshape(5, 2)
        except Exception:
            return None
    # if normalized (<=1), scale
    if arr.max() <= 1.01:
        arr[:, 0] *= w
        arr[:, 1] *= h
    return arr.astype(np.float32)


def align_face_to_112(frame_rgb: np.ndarray, landmarks: Optional[np.ndarray], bbox: Optional[List[int]]) -> np.ndarray:
    H, W = frame_rgb.shape[:2]
    if landmarks is not None:
        try:
            M, _ = cv2.estimateAffinePartial2D(np.array(landmarks, dtype=np.float32), REF_KPS_112, method=cv2.RANSAC, ransacReprojThreshold=5.0)
            if M is not None:
                return cv2.warpAffine(frame_rgb, M, (112, 112), flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REPLICATE)
        except Exception:
            pass
    if bbox is not None:
        x1, y1, x2, y2 = bbox
        x1 = max(0, int(round(x1))); y1 = max(0, int(round(y1)))
        x2 = min(W - 1, int(round(x2))); y2 = min(H - 1, int(round(y2)))
        if x2 > x1 and y2 > y1:
            margin = int(round(0.2 * max(x2 - x1, y2 - y1)))
            cx1 = max(0, x1 - margin); cy1 = max(0, y1 - margin)
            cx2 = min(W - 1, x2 + margin); cy2 = min(H - 1, y2 + margin)
            crop = frame_rgb[cy1:cy2, cx1:cx2]
            if crop.size != 0:
                return cv2.resize(crop, (112, 112), interpolation=cv2.INTER_LINEAR)
    # fallback center-crop
    hmin = min(H, W)
    cy = H // 2; cx = W // 2; half = hmin // 2
    crop = frame_rgb[max(0, cy - half):min(H, cy + half), max(0, cx - half):min(W, cx + half)]
    if crop.size == 0:
        return cv2.resize(frame_rgb, (112, 112), interpolation=cv2.INTER_LINEAR)
    return cv2.resize(crop, (112, 112), interpolation=cv2.INTER_LINEAR)


# -----------------------
# Main processing
# -----------------------
def run_embedding_insertion(root_folder: str, zoo_path: str, batch_size: int = 32, min_score: float = 0.5, overwrite_centroid: bool = False, verify: bool = False):
    root_folder = os.path.expanduser(root_folder)
    zoo_path = os.path.expanduser(zoo_path)

    if not os.path.isdir(root_folder):
        logging.error("root_folder does not exist or is not a directory: %s", root_folder)
        return

    # model names (same as your extractor)
    detection_model_name = "retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1"
    embedding_model_name = "arcface_mobilefacenet--112x112_quant_hailort_hailo8l_1"

    zoo = connect_zoo(zoo_path)
    logging.info("Loading models...")
    detector = zoo.load_model(detection_model_name)
    embedder = zoo.load_model(embedding_model_name)

    # ensure detector expects RGB numpy
    try:
        detector.input_numpy_colorspace = "RGB"
    except Exception:
        pass
    try:
        embedder.input_numpy_colorspace = "RGB"
    except Exception:
        pass

    # Chromadb persistent client
    db_path = os.path.expanduser("db/face_embeddings_db")
    os.makedirs(db_path, exist_ok=True)
    client = chromadb.PersistentClient(path=db_path)
    collection = client.get_or_create_collection(name="face_embeddings", metadata={"hnsw:space": "cosine"})

    # Walk persons
    persons = [d for d in os.listdir(root_folder) if os.path.isdir(os.path.join(root_folder, d))]
    persons.sort()
    logging.info("Found %d persons in %s", len(persons), root_folder)

    total_added = 0
    for person in persons:
        person_dir = os.path.join(root_folder, person)
        imgs = [f for f in os.listdir(person_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        imgs.sort()
        if not imgs:
            logging.info("Skipping %s (no images)", person)
            continue

        logging.info("Processing person '%s' (%d images)...", person, len(imgs))

        per_person_embeddings = []
        per_person_ids = []
        per_person_metadatas = []

        # Process each image
        for idx, fname in enumerate(imgs):
            path = os.path.join(person_dir, fname)
            bgr = cv2.imread(path)
            if bgr is None:
                logging.warning("Failed to load %s, skipping", path)
                continue
            rgb = cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)
            H, W = rgb.shape[:2]

            # detect
            try:
                det_out = detector.predict(rgb)
            except Exception as e:
                logging.warning("Detector predict failed on %s: %s", path, e)
                continue

            results = safe_get_results(det_out)
            if not results:
                logging.debug("No detections for %s", path)
                continue

            # choose best by score
            best = max(results, key=lambda r: float(r.get("score", 0.0)))
            score = float(best.get("score", 0.0))
            if score < min_score:
                logging.debug("Low score %.3f for %s (min_score=%.3f) — skipping", score, path, min_score)
                continue

            # parse bbox/landmarks
            raw_bbox = best.get("bbox") or best.get("box") or best.get("rectangle")
            raw_lm = best.get("landmarks") or best.get("landmark") or best.get("keypoints")
            # safe bbox parse (attempt best-effort)
            bbox = None
            try:
                if raw_bbox is not None:
                    a = np.asarray(raw_bbox, dtype=float).flatten()
                    if a.size == 4:
                        x1, y1, x2, y2 = a.tolist()
                        if (x2 - x1) <= 1.0: x2 = x1 + x2
                        if (y2 - y1) <= 1.0: y2 = y1 + y2
                        bbox = [int(round(x1)), int(round(y1)), int(round(x2)), int(round(y2))]
            except Exception:
                bbox = None

            lm = safe_parse_landmarks(raw_lm, W, H)
            aligned = align_face_to_112(rgb, lm, bbox)

            # embed
            try:
                emb_out = embedder.predict(aligned)
            except Exception as e:
                logging.warning("Embedder predict failed for %s: %s", path, e)
                continue

            emb_res_list = safe_get_results(emb_out)
            if not emb_res_list:
                logging.warning("No embedding results for %s", path)
                continue

            emb_vec = safe_extract_embedding(emb_res_list[0])
            if emb_vec is None:
                logging.warning("Could not extract embedding vector for %s", path)
                continue

            # ensure 1D numpy
            emb = np.asarray(emb_vec, dtype=float).flatten()
            # L2 normalize
            nrm = np.linalg.norm(emb)
            if nrm <= 1e-8:
                logging.warning("Zero-norm embedding for %s, skipping", path)
                continue
            emb = (emb / nrm).astype(float)

            # prepare DB insert item
            img_base = os.path.splitext(fname)[0]
            vid = f"{person}_{img_base}"
            vmeta = {"person": person, "filename": fname, "score": float(score), "ts": int(time.time())}

            per_person_embeddings.append(emb.tolist())
            per_person_ids.append(vid)
            per_person_metadatas.append(vmeta)

            logging.debug("Prepared embedding for %s id=%s", path, vid)

            # batch flush if enough accumulated
            if len(per_person_embeddings) >= batch_size:
                added = batch_add_to_collection(collection, per_person_ids, per_person_embeddings, per_person_metadatas)
                total_added += added
                # clear per-person buffers
                per_person_embeddings = []
                per_person_ids = []
                per_person_metadatas = []

        # flush remaining for person
        if per_person_embeddings:
            added = batch_add_to_collection(collection, per_person_ids, per_person_embeddings, per_person_metadatas)
            total_added += added

        # After storing all per-image embeddings for this person, compute centroid if at least one vector stored
        # Fetch embeddings we just stored (simpler: use the per-person ids created earlier but if we batched and cleared, better to query collection by metadata or re-load images)
        # For reliability, we will compute centroid from files we processed in this run by searching collection for ids matching prefix.
        # Simpler approach: compute centroid from local list by re-reading all vectors from the collection using the person prefix.

        # Gather all vectors for this person by listing with metadata (Chroma doesn't provide a direct metadata search in all versions)
        # We'll attempt to fetch by ids using the image filenames we used above (if they exist). To be resilient, we will collect ids again from directory listing:
        person_ids = []
        for fname in [f for f in os.listdir(person_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]:
            id_candidate = f"{person}_{os.path.splitext(fname)[0]}"
            person_ids.append(id_candidate)

        # Try to get stored vectors using collection.get(ids=person_ids). Include embeddings explicitly.
        try:
            resp = collection.get(ids=person_ids, include=["embeddings"])
            vectors = resp.get("embeddings", [])
            if vectors is None:
                vectors = []
            logging.info("Retrieved %d stored embeddings for person %s from collection", len(vectors), person)
            # If embeddings list is empty, try to compute centroid from local files we inserted earlier by reading what exists in collection
            if len(vectors) == 0:
                logging.warning("No vectors returned for person %s when computing centroid; will fallback to re-embedding", person)
        except Exception as e:
            logging.warning("Failed to retrieve embeddings for person %s: %s; will fallback to re-embedding", person, e)
            vectors = []

        # If vectors empty, attempt to compute centroid by reading each image file and embedding again (fallback).
        centroid_vector = None
        if vectors:
            try:
                arr = np.asarray(vectors, dtype=float)
                if arr.ndim == 2 and arr.shape[0] > 0:
                    mean_vec = arr.mean(axis=0)
                    mean_norm = np.linalg.norm(mean_vec)
                    if mean_norm > 0:
                        centroid_vector = (mean_vec / mean_norm).astype(float)
            except Exception as e:
                logging.warning("Failed to compute centroid from stored vectors for %s: %s", person, e)

        # If still no centroid (vectors empty), compute centroid by re-embedding images in the folder
        if centroid_vector is None:
            logging.info("Computing centroid for %s by re-embedding local images (stored embeddings unavailable).", person)
            emb_list = []
            for fname in [f for f in os.listdir(person_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]:
                path = os.path.join(person_dir, fname)
                bgr = cv2.imread(path)
                if bgr is None:
                    continue
                rgb = cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)
                H, W = rgb.shape[:2]
                try:
                    det_out = detector.predict(rgb)
                except Exception:
                    continue
                results = safe_get_results(det_out)
                if not results:
                    continue
                best = max(results, key=lambda r: float(r.get("score", 0.0)))
                if float(best.get("score", 0.0)) < min_score:
                    continue
                lm = safe_parse_landmarks(best.get("landmarks"), W, H)
                bbox = None
                try:
                    raw_bbox = best.get("bbox") or best.get("box") or best.get("rectangle")
                    if raw_bbox is not None:
                        a = np.asarray(raw_bbox, dtype=float).flatten()
                        if a.size == 4:
                            x1, y1, x2, y2 = a.tolist()
                            if (x2 - x1) <= 1.0: x2 = x1 + x2
                            if (y2 - y1) <= 1.0: y2 = y1 + y2
                            bbox = [int(round(x1)), int(round(y1)), int(round(x2)), int(round(y2))]
                except Exception:
                    bbox = None
                aligned = align_face_to_112(rgb, lm, bbox)
                try:
                    emb_out = embedder.predict(aligned)
                except Exception:
                    continue
                emb_vec = safe_extract_embedding(safe_get_results(emb_out)[0]) if safe_get_results(emb_out) else None
                if emb_vec is None:
                    continue
                emb = np.asarray(emb_vec, dtype=float).flatten()
                nrm = np.linalg.norm(emb)
                if nrm > 1e-8:
                    emb_list.append((emb / nrm).astype(float))
            if emb_list:
                arr = np.vstack(emb_list)
                mean_vec = arr.mean(axis=0)
                mean_norm = np.linalg.norm(mean_vec)
                if mean_norm > 0:
                    centroid_vector = (mean_vec / mean_norm).astype(float)

        # Upsert centroid if computed
        if centroid_vector is not None:
            centroid_id = f"{person}__centroid"
            centroid_meta = {"person": person, "type": "centroid", "n_vectors": len(imgs), "ts": int(time.time())}
            try:
                # try add first
                collection.add(embeddings=[centroid_vector.tolist()], metadatas=[centroid_meta], ids=[centroid_id])
                logging.info("Inserted centroid for %s id=%s", person, centroid_id)
            except Exception as e:
                # likely duplicate id; update if overwrite_centroid True else skip
                logging.debug("centroid add exception for %s: %s", person, e)
                if overwrite_centroid:
                    try:
                        collection.update(ids=[centroid_id], embeddings=[centroid_vector.tolist()], metadatas=[centroid_meta])
                        logging.info("Updated centroid for %s id=%s", person, centroid_id)
                    except Exception as e2:
                        logging.warning("Failed to update centroid for %s: %s", person, e2)
                else:
                    logging.info("Centroid for %s already exists; use --overwrite_centroid to replace", person)

        logging.info("Done person '%s'. Total vectors stored so far: %d", person, total_added)

    logging.info("Processing finished. Total added vectors (approx): %d", total_added)


def batch_add_to_collection(collection, ids: List[str], embeddings: List[List[float]], metadatas: List[dict]) -> int:
    """
    Try to add a batch, fallback to update when duplicate id error occurs.
    Returns number of vectors added/updated (best-effort).
    """
    if not ids:
        return 0
    try:
        collection.add(embeddings=embeddings, metadatas=metadatas, ids=ids)
        logging.info("Added %d vectors to collection.", len(ids))
        return len(ids)
    except Exception as e:
        logging.debug("Batch add failed (attempting per-item upsert): %s", e)
        added = 0
        for i, vid in enumerate(ids):
            emb = embeddings[i]
            meta = metadatas[i]
            try:
                collection.add(embeddings=[emb], metadatas=[meta], ids=[vid])
                added += 1
            except Exception:
                # try update (if id exists)
                try:
                    collection.update(ids=[vid], embeddings=[emb], metadatas=[meta])
                    added += 1
                except Exception as e2:
                    logging.warning("Failed to add/update id=%s: %s", vid, e2)
        return added


if __name__ == "__main__":
    p = argparse.ArgumentParser()
    p.add_argument("--root_folder", type=str, default="faces", help="Path to root folder containing person subfolders")
    p.add_argument("--zoo_path", type=str, default="~/degirum-zoo", help="Path to DeGirum model zoo")
    p.add_argument("--batch_size", type=int, default=32, help="DB batch size for inserts")
    p.add_argument("--min_score", type=float, default=0.5, help="Minimum face detection score to accept")
    p.add_argument("--overwrite_centroid", action="store_true", help="If set, overwrite existing centroid entries")
    p.add_argument("--verify", action="store_true", help="Print verification info (one-time)")
    args = p.parse_args()

    run_embedding_insertion(args.root_folder, args.zoo_path, batch_size=args.batch_size, min_score=args.min_score, overwrite_centroid=args.overwrite_centroid, verify=args.verify)
    # python jk_face_embed_store.py